{% load static %}

<a href="{{ href }}"
   :class="isActive('{{ item_key }}') ? 'sidebar-nav-active' : 'sidebar-nav-inactive'"
   @click="setActive('{{ item_key }}')"
   class="group relative flex items-center justify-between px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 hover:scale-[1.02] active:scale-[0.98] overflow-hidden">

    <!-- Active indicator line -->
    <div :class="isActive('{{ item_key }}') ? 'opacity-100' : 'opacity-0'"
         class="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-primary-400 to-primary-600 rounded-r-full transition-opacity duration-300"></div>

    <!-- Background glow for active state -->
    <div :class="isActive('{{ item_key }}') ? 'opacity-100' : 'opacity-0'"
         class="absolute inset-0 bg-gradient-to-r from-primary-600/20 via-primary-500/10 to-transparent rounded-xl transition-opacity duration-300"></div>

    <!-- Hover background -->
    <div class="absolute inset-0 bg-slate-700/30 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

    <div class="relative flex items-center flex-1">
        <div class="w-10 h-10 rounded-lg flex items-center justify-center mr-3 transition-all duration-300 group-hover:scale-110"
             :class="isActive('{{ item_key }}') ? 'bg-primary-600/30 text-primary-300' : 'bg-slate-700/50 text-slate-400 group-hover:bg-slate-600/50 group-hover:text-slate-300'">
            {{ icon_svg|safe }}
        </div>
        <div class="flex-1 min-w-0">
            <span class="block font-semibold transition-colors duration-300"
                  :class="isActive('{{ item_key }}') ? 'text-white' : 'text-slate-300 group-hover:text-white'">{{ label }}</span>
            {% if description %}
            <span class="block text-xs transition-colors duration-300"
                  :class="isActive('{{ item_key }}') ? 'text-primary-200' : 'text-slate-500 group-hover:text-slate-400'">{{ description }}</span>
            {% endif %}
        </div>
    </div>

    <div class="relative flex items-center space-x-2">
        {% if badge_count %}
        <span class="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-gradient-to-r from-red-500 to-red-600 rounded-full shadow-lg animate-pulse ring-2 ring-red-500/30">
            {{ badge_count }}
        </span>
        {% endif %}

        <!-- Arrow indicator -->
        <svg class="w-4 h-4 transition-all duration-300 opacity-0 group-hover:opacity-100 group-hover:translate-x-1"
             :class="isActive('{{ item_key }}') ? 'opacity-100 text-primary-300' : 'text-slate-400'"
             fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
    </div>
</a>