{% extends 'dashboards/base_dashboard.html' %}

{% block dashboard_title %}Seller Dashboard{% endblock %}
{% block active_nav_item %}overview{% endblock %}

{% block sidebar_icon %}
<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
</svg>
{% endblock %}

{% block sidebar_title %}Seller Hub{% endblock %}

{% block sidebar_navigation %}
<div x-data="sidebarNav()" class="space-y-2">
    <!-- Overview -->
    {% include 'components/sidebar_nav_item.html' with href='/seller-dashboard/' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Dashboard' description='Overview & insights' item_key='overview' %}

    <!-- My Listings -->
    {% include 'components/sidebar_nav_item.html' with href='/dashboard/listings/' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path></svg>' label='My Listings' description='Manage properties' item_key='listings' badge_count=total_listings %}

    <!-- Create Listing -->
    {% include 'components/sidebar_nav_item.html' with href='/dashboard/create/' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>' label='Create Listing' description='Add new property' item_key='create' %}

    <!-- Inquiries -->
    {% include 'components/sidebar_nav_item.html' with href='/dashboard/inquiries/' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>' label='Inquiries' description='Buyer communications' item_key='inquiries' badge_count=unread_inquiries %}

    <!-- Performance -->
    {% include 'components/sidebar_nav_item.html' with href='/dashboard/performance/' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Performance' description='Analytics & reports' item_key='performance' %}

    <!-- Divider -->
    <div class="my-6">
        <div class="h-px bg-gradient-to-r from-transparent via-slate-600 to-transparent"></div>
    </div>

    <!-- Seller Resources -->
    {% include 'components/sidebar_nav_item.html' with href='/dashboard/resources/' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path></svg>' label='Seller Resources' description='Tips & guides' item_key='resources' %}

    <!-- Help & Support -->
    {% include 'components/sidebar_nav_item.html' with href='/dashboard/support/' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path></svg>' label='Support' description='Help & documentation' item_key='support' %}
</div>
{% endblock %}

{% block page_description %}Manage your land listings and connect with potential buyers{% endblock %}

{% block page_actions %}
<a href="{% url 'seller_listings' %}" class="btn btn-secondary btn-md">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
    </svg>
    View All Listings
</a>
<a href="{% url 'seller_create_listing' %}" class="btn btn-primary btn-md">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
    </svg>
    Create New Listing
</a>
{% endblock %}

{% block dashboard_content %}
<!-- Quick Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Listings -->
    <div class="group relative bg-white rounded-2xl shadow-sm border border-secondary-100 hover:shadow-xl hover:border-blue-200 transition-all duration-300 overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        <div class="relative p-6">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                    </div>
                    <p class="text-sm font-medium text-secondary-600 mb-1">Total Listings</p>
                    <p class="text-3xl font-bold text-secondary-900 mb-2">{{ total_listings|default:"8" }}</p>
                    <div class="flex items-center space-x-1">
                        <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-xs font-medium text-green-600">All your listings</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Listings -->
    <div class="group relative bg-white rounded-2xl shadow-sm border border-secondary-100 hover:shadow-xl hover:border-green-200 transition-all duration-300 overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-br from-green-50/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        <div class="relative p-6">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    </div>
                    <p class="text-sm font-medium text-secondary-600 mb-1">Active Listings</p>
                    <p class="text-3xl font-bold text-secondary-900 mb-2">{{ active_listings|default:"6" }}</p>
                    <div class="flex items-center space-x-1">
                        <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-xs font-medium text-green-600">Approved & visible</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Inquiries -->
    <div class="group relative bg-white rounded-2xl shadow-sm border border-secondary-100 hover:shadow-xl hover:border-purple-200 transition-all duration-300 overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-br from-purple-50/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        <div class="relative p-6">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                        </div>
                        <div class="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                    </div>
                    <p class="text-sm font-medium text-secondary-600 mb-1">Total Inquiries</p>
                    <p class="text-3xl font-bold text-secondary-900 mb-2">{{ total_inquiries|default:"24" }}</p>
                    <div class="flex items-center space-x-1">
                        {% if unread_inquiries %}
                            <svg class="w-4 h-4 text-amber-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-xs font-medium text-amber-600">{{ unread_inquiries }} unread</span>
                        {% else %}
                            <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-xs font-medium text-green-600">All responded</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Review -->
    <div class="group relative bg-white rounded-2xl shadow-sm border border-secondary-100 hover:shadow-xl hover:border-amber-200 transition-all duration-300 overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-br from-amber-50/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        <div class="relative p-6">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="w-2 h-2 bg-amber-400 rounded-full animate-pulse"></div>
                    </div>
                    <p class="text-sm font-medium text-secondary-600 mb-1">Pending Review</p>
                    <p class="text-3xl font-bold text-secondary-900 mb-2">{{ pending_listings|default:"2" }}</p>
                    <div class="flex items-center space-x-1">
                        <svg class="w-4 h-4 text-amber-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-xs font-medium text-amber-600">Awaiting approval</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Recent Listings -->
    <div class="lg:col-span-2">
        <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 overflow-hidden">
            <div class="px-6 py-5 border-b border-secondary-100 bg-gradient-to-r from-secondary-50/50 to-transparent">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-xl font-bold text-secondary-900">Recent Listings</h3>
                        <p class="text-sm text-secondary-600 mt-1">Your latest property listings and their status</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span class="text-xs font-medium text-secondary-500">Live</span>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    {% for listing in recent_listings %}
                    <div class="flex items-center space-x-4 p-4 bg-secondary-50 rounded-xl hover:bg-secondary-100 transition-colors duration-200 group">
                        <div class="w-16 h-16 bg-secondary-200 rounded-lg flex items-center justify-center overflow-hidden">
                            {% if listing.images.first %}
                                <img src="{{ listing.images.first.image.url }}" alt="{{ listing.title }}" class="w-full h-full object-cover">
                            {% else %}
                                <svg class="w-8 h-8 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            {% endif %}
                        </div>
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-secondary-900">{{ listing.title }}</h4>
                            <p class="text-xs text-secondary-500">{{ listing.location }}</p>
                            <p class="text-xs text-secondary-500">Created {{ listing.created_at|timesince }} ago</p>
                            <div class="flex items-center mt-2">
                                {% include 'components/listing_status_badge.html' with listing=listing %}
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-semibold text-secondary-900">${{ listing.price|floatformat:0 }}</p>
                            <p class="text-xs text-secondary-500">${{ listing.price|div:listing.size_acres|floatformat:0 }}/acre</p>
                            <a href="{% url 'seller_edit_listing' listing.id %}" class="text-xs text-primary-600 hover:text-primary-700">Edit</a>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 text-secondary-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        <p class="text-secondary-500 mb-4">No listings yet</p>
                        <a href="{% url 'seller_create_listing' %}" class="btn btn-primary btn-sm">Create Your First Listing</a>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions & Recent Inquiries -->
    <div class="space-y-6">
        <!-- Quick Actions -->
        <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 overflow-hidden">
            <div class="px-6 py-5 border-b border-secondary-100 bg-gradient-to-r from-secondary-50/50 to-transparent">
                <h3 class="text-xl font-bold text-secondary-900">Quick Actions</h3>
                <p class="text-sm text-secondary-600 mt-1">Common seller tasks</p>
            </div>
            <div class="p-6 space-y-4">
                <!-- Create Listing Action -->
                <a href="{% url 'seller_create_listing' %}"
                   class="group relative w-full flex items-center p-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl shadow-lg hover:shadow-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div class="relative flex items-center w-full">
                        <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-white">Create New Listing</h4>
                            <p class="text-blue-100 text-sm">List your property for sale</p>
                        </div>
                        <div class="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </div>
                    </div>
                </a>

                <!-- View Inquiries Action -->
                <a href="{% url 'seller_inquiries' %}"
                   class="group relative w-full flex items-center p-4 bg-white border-2 border-secondary-200 text-secondary-700 rounded-xl shadow-sm hover:shadow-lg hover:border-secondary-300 hover:bg-secondary-50 transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98]">
                    <div class="flex items-center w-full">
                        <div class="w-10 h-10 bg-secondary-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-secondary-200 group-hover:scale-110 transition-all duration-300">
                            <svg class="w-5 h-5 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-secondary-900">View Inquiries</h4>
                            <p class="text-secondary-600 text-sm">Manage buyer communications</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            {% if unread_inquiries > 0 %}
                                <span class="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-gradient-to-r from-red-500 to-red-600 rounded-full shadow-lg animate-pulse ring-2 ring-red-500/30">
                                    {{ unread_inquiries }}
                                </span>
                            {% endif %}
                            <div class="w-6 h-6 bg-secondary-100 rounded-full flex items-center justify-center group-hover:bg-secondary-200 transition-colors duration-300">
                                <svg class="w-3 h-3 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </a>

                <!-- Manage Listings Action -->
                <a href="{% url 'seller_listings' %}"
                   class="group relative w-full flex items-center p-4 bg-white border-2 border-secondary-200 text-secondary-700 rounded-xl shadow-sm hover:shadow-lg hover:border-secondary-300 hover:bg-secondary-50 transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98]">
                    <div class="flex items-center w-full">
                        <div class="w-10 h-10 bg-secondary-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-secondary-200 group-hover:scale-110 transition-all duration-300">
                            <svg class="w-5 h-5 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-secondary-900">Manage Listings</h4>
                            <p class="text-secondary-600 text-sm">Edit and update properties</p>
                        </div>
                        <div class="w-6 h-6 bg-secondary-100 rounded-full flex items-center justify-center group-hover:bg-secondary-200 transition-colors duration-300">
                            <svg class="w-3 h-3 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <!-- Recent Inquiries -->
        <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 overflow-hidden">
            <div class="px-6 py-5 border-b border-secondary-100 bg-gradient-to-r from-secondary-50/50 to-transparent">
                <h3 class="text-xl font-bold text-secondary-900">Recent Inquiries</h3>
                <p class="text-sm text-secondary-600 mt-1">Latest buyer communications</p>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    {% for inquiry in recent_inquiries %}
                    <div class="flex items-center space-x-3 p-3 bg-secondary-50 rounded-xl hover:bg-secondary-100 transition-colors duration-200 group">
                        <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                            <span class="text-primary-600 font-medium text-sm">{{ inquiry.buyer.first_name.0|default:inquiry.buyer.username.0|upper }}</span>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-secondary-900">{{ inquiry.buyer.get_full_name|default:inquiry.buyer.username }}</p>
                            <p class="text-xs text-secondary-500">{{ inquiry.land.title|truncatechars:30 }}</p>
                            <p class="text-xs text-secondary-500">{{ inquiry.created_at|timesince }} ago</p>
                        </div>
                        {% if not inquiry.is_read %}
                            <span class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></span>
                        {% endif %}
                        <a href="{% url 'seller_inquiry_detail' inquiry.id %}" class="text-xs text-primary-600 hover:text-primary-700">View</a>
                    </div>
                    {% empty %}
                    <div class="text-center py-6">
                        <svg class="w-8 h-8 text-secondary-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                        <p class="text-secondary-500 text-sm">No inquiries yet</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}