# LandHub Test Accounts

This document contains login credentials for testing the LandHub platform. All accounts are pre-configured with appropriate roles and sample data.

## 🚀 Quick Setup

To create all test accounts and sample data, run:

```bash
python manage.py create_sample_data
```

To create only user accounts without sample data:

```bash
python manage.py create_sample_data --users-only
```

---

## 👑 Administrator Accounts

### Primary Admin Account
- **Username:** `admin`
- **Password:** `admin123`
- **Email:** <EMAIL>
- **Role:** Administrator
- **Access:** Full platform access, user management, listing approval
- **Dashboard:** `/admin-dashboard/`

### Super Admin Account
- **Username:** `superadmin`
- **Password:** `admin123`
- **Email:** <EMAIL>
- **Role:** Administrator
- **Access:** Full platform access, user management, listing approval
- **Dashboard:** `/admin-dashboard/`

---

## 🏢 Seller Accounts

### <PERSON> - Agricultural Specialist
- **Username:** `seller_john`
- **Password:** `seller123`
- **Email:** <EMAIL>
- **Phone:** ******-0201
- **Specialization:** Agricultural properties in Texas
- **Dashboard:** `/seller-dashboard/`

### Sarah Johnson - Recreational Land Expert
- **Username:** `seller_sarah`
- **Password:** `seller123`
- **Email:** <EMAIL>
- **Phone:** ******-0202
- **Specialization:** Recreational and residential land sales
- **Dashboard:** `/seller-dashboard/`

### Mike Davis - Commercial Land Specialist
- **Username:** `seller_mike`
- **Password:** `seller123`
- **Email:** <EMAIL>
- **Phone:** ******-0203
- **Specialization:** Commercial land with 15+ years experience
- **Dashboard:** `/seller-dashboard/`

### Lisa Wilson - Ranch Specialist
- **Username:** `seller_lisa`
- **Password:** `seller123`
- **Email:** <EMAIL>
- **Phone:** ******-0204
- **Specialization:** Family-owned ranch properties
- **Dashboard:** `/seller-dashboard/`

### Demo Seller Account
- **Username:** `seller_demo`
- **Password:** `seller123`
- **Email:** <EMAIL>
- **Phone:** ******-0205
- **Purpose:** Testing platform features
- **Dashboard:** `/seller-dashboard/`

---

## 🏠 Buyer Accounts

### Alex Brown - Organic Farmer
- **Username:** `buyer_alex`
- **Password:** `buyer123`
- **Email:** <EMAIL>
- **Phone:** ******-0301
- **Interest:** Agricultural land for organic farming
- **Dashboard:** `/buyer-dashboard/`

### Emma Taylor - Family Retreat Seeker
- **Username:** `buyer_emma`
- **Password:** `buyer123`
- **Email:** <EMAIL>
- **Phone:** ******-0302
- **Interest:** Recreational property for family retreat and hunting
- **Dashboard:** `/buyer-dashboard/`

### David Martinez - Investment Buyer
- **Username:** `buyer_david`
- **Password:** `buyer123`
- **Email:** <EMAIL>
- **Phone:** ******-0303
- **Interest:** Commercial development opportunities
- **Dashboard:** `/buyer-dashboard/`

### Jennifer Garcia - First-Time Buyer
- **Username:** `buyer_jennifer`
- **Password:** `buyer123`
- **Email:** <EMAIL>
- **Phone:** ******-0304
- **Interest:** Residential building lots
- **Dashboard:** `/buyer-dashboard/`

### Robert Lee - Rancher
- **Username:** `buyer_robert`
- **Password:** `buyer123`
- **Email:** <EMAIL>
- **Phone:** ******-0305
- **Interest:** Grazing land for ranch expansion
- **Dashboard:** `/buyer-dashboard/`

### Maria Rodriguez - Environmental Consultant
- **Username:** `buyer_maria`
- **Password:** `buyer123`
- **Email:** <EMAIL>
- **Phone:** ******-0306
- **Interest:** Conservation land
- **Dashboard:** `/buyer-dashboard/`

### Demo Buyer Account
- **Username:** `buyer_demo`
- **Password:** `buyer123`
- **Email:** <EMAIL>
- **Phone:** ******-0307
- **Purpose:** Testing platform features
- **Dashboard:** `/buyer-dashboard/`

---

## 🔐 Login Instructions

1. **Navigate to Login Page:** Go to `/auth/login/` or click "Login" on the homepage
2. **Enter Credentials:** Use any username and password combination from above
3. **Automatic Redirect:** Users are automatically redirected to their role-specific dashboard:
   - Admins → `/admin-dashboard/`
   - Sellers → `/seller-dashboard/`
   - Buyers → `/buyer-dashboard/`

---

## 📊 Sample Data Included

When running the full `create_sample_data` command, the following sample data is created:

### Land Listings
- **5 Featured Listings:** Prime agricultural land, residential lots, commercial opportunities, hunting ranch, waterfront property
- **15 Additional Listings:** Various property types with realistic pricing and descriptions
- **Property Types:** Agricultural, Residential, Commercial, Recreational
- **Status Variety:** Pending, Approved, Rejected, Draft
- **Locations:** Major Texas cities and counties

### User Interactions
- **25 Sample Inquiries:** Realistic buyer inquiries with varied messages
- **20 Favorites:** Buyer-saved properties for testing favorites functionality
- **Mixed Read Status:** Some inquiries marked as read/unread for testing

---

## 🛠️ Testing Scenarios

### Admin Testing
- Log in as `admin` or `superadmin`
- Review pending listings for approval
- Manage user accounts and roles
- View platform analytics and reports

### Seller Testing
- Log in as any seller account (e.g., `seller_john`)
- Create new property listings
- Respond to buyer inquiries
- View listing performance metrics

### Buyer Testing
- Log in as any buyer account (e.g., `buyer_alex`)
- Browse available properties
- Save properties to favorites
- Send inquiries to sellers
- Track inquiry responses

---

## 🔄 Resetting Data

To reset all sample data and recreate fresh accounts:

```bash
# Delete existing data (optional)
python manage.py flush

# Recreate sample data
python manage.py create_sample_data
```

---

## 📝 Notes

- All passwords are intentionally simple for testing purposes
- Email addresses use example.com domain (safe for testing)
- Phone numbers use the ******-0xxx format (reserved for testing)
- User profiles include realistic bios and contact information
- All accounts are immediately active and ready for use

---

## 🆘 Support

If you encounter any issues with these test accounts:

1. Verify the accounts were created by running the management command
2. Check that you're using the correct username/password combination
3. Ensure you're accessing the correct dashboard URL for the user role
4. Contact the development team if problems persist

**Last Updated:** December 2024
