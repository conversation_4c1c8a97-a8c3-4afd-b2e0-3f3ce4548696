{% extends 'dashboards/base_dashboard.html' %}

{% block dashboard_title %}My Listings{% endblock %}
{% block active_nav_item %}listings{% endblock %}

{% block sidebar_icon %}
<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
</svg>
{% endblock %}

{% block sidebar_title %}Seller Hub{% endblock %}

{% block sidebar_navigation %}
<div x-data="sidebarNav()">
    <!-- Overview -->
    {% include 'components/sidebar_nav_item.html' with href='{% url "seller_dashboard" %}' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Overview' item_key='overview' %}

    <!-- My Listings -->
    {% include 'components/sidebar_nav_item.html' with href='{% url "seller_listings" %}' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path></svg>' label='My Listings' item_key='listings' %}

    <!-- Create Listing -->
    {% include 'components/sidebar_nav_item.html' with href='{% url "seller_create_listing" %}' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>' label='Create Listing' item_key='create' %}

    <!-- Inquiries -->
    {% include 'components/sidebar_nav_item.html' with href='{% url "seller_inquiries" %}' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>' label='Inquiries' item_key='inquiries' %}

    <!-- Divider -->
    <div class="border-t border-secondary-200 my-4"></div>

    <!-- Help & Support -->
    {% include 'components/sidebar_nav_item.html' with href='#help' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>' label='Help & Support' item_key='help' %}
</div>
{% endblock %}

{% block page_title %}My Listings{% endblock %}
{% block page_description %}Manage all your land listings and track their performance{% endblock %}

{% block page_actions %}
<a href="{% url 'seller_create_listing' %}" class="btn btn-primary btn-md">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
    </svg>
    Create New Listing
</a>
{% endblock %}

{% block dashboard_content %}
<!-- Filters and Search -->
<div class="bg-white rounded-lg shadow-sm border border-secondary-200 p-6 mb-6">
    <form method="get" class="flex flex-col md:flex-row gap-4" hx-get="{% url 'seller_listings' %}" hx-target="#listings-container" hx-trigger="change, submit">
        <div class="flex-1">
            <input type="text" name="search" value="{{ search_query }}" placeholder="Search your listings..." class="form-input">
        </div>
        <div class="w-full md:w-48">
            <select name="status" class="form-select">
                <option value="">All Status</option>
                {% for value, label in status_choices %}
                    <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>{{ label }}</option>
                {% endfor %}
            </select>
        </div>
        <button type="submit" class="btn btn-secondary btn-md">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            Search
        </button>
    </form>
</div>

<!-- Listings Grid -->
<div id="listings-container">
    {% if page_obj %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {% for listing in page_obj %}
            <div class="card hover:shadow-lg transition-all duration-200">
                <div class="relative">
                    {% if listing.images.first %}
                        <img src="{{ listing.images.first.image.url }}" alt="{{ listing.title }}" class="w-full h-48 object-cover">
                    {% else %}
                        <div class="w-full h-48 bg-secondary-100 flex items-center justify-center">
                            <svg class="w-12 h-12 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    {% endif %}
                    <div class="absolute top-3 right-3">
                        {% include 'components/listing_status_badge.html' with listing=listing %}
                    </div>
                </div>
                
                <div class="card-body">
                    <h3 class="text-lg font-semibold text-secondary-900 mb-2">{{ listing.title }}</h3>
                    <p class="text-sm text-secondary-600 mb-3">{{ listing.location }}</p>
                    
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <p class="text-xl font-bold text-secondary-900">${{ listing.price|floatformat:0 }}</p>
                            <p class="text-sm text-secondary-500">{{ listing.size_acres }} acres</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-medium text-secondary-700">${{ listing.price|div:listing.size_acres|floatformat:0 }}/acre</p>
                            <p class="text-xs text-secondary-500">{{ listing.get_property_type_display }}</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between text-xs text-secondary-500 mb-4">
                        <span>Created {{ listing.created_at|timesince }} ago</span>
                        <span>Updated {{ listing.updated_at|timesince }} ago</span>
                    </div>
                    
                    <div class="flex space-x-2">
                        <a href="{% url 'seller_edit_listing' listing.id %}" class="flex-1 btn btn-secondary btn-sm">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Edit
                        </a>
                        <button class="btn btn-ghost btn-sm" title="View Details">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <div class="flex items-center justify-between">
            <div class="text-sm text-secondary-700">
                Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} listings
            </div>
            <div class="flex space-x-2">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" class="btn btn-secondary btn-sm">Previous</a>
                {% endif %}
                
                <span class="flex items-center px-3 py-2 text-sm text-secondary-700">
                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                </span>
                
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" class="btn btn-secondary btn-sm">Next</a>
                {% endif %}
            </div>
        </div>
        {% endif %}
    {% else %}
        <div class="text-center py-12">
            <svg class="w-16 h-16 text-secondary-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
            <h3 class="text-lg font-medium text-secondary-900 mb-2">No listings found</h3>
            <p class="text-secondary-600 mb-6">{% if search_query or status_filter %}Try adjusting your search criteria{% else %}Start by creating your first land listing{% endif %}</p>
            <a href="{% url 'seller_create_listing' %}" class="btn btn-primary btn-md">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Create New Listing
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}