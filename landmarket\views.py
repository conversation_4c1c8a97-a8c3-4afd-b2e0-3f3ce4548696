from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Q, Count, Max
from django.core.paginator import Paginator
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from django.forms import modelformset_factory
from .models import UserProfile, Land, Inquiry, Favorite, LandImage
from .forms import LandListingForm, LandImageForm, MultipleImageUploadForm, InquiryResponseForm
import json


def home(request):
    """Home page view"""
    return render(request, 'home.html')


@login_required
def admin_dashboard(request):
    """Admin dashboard view with analytics"""
    if not request.user.profile.role == 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('auth:dashboard_redirect')
    
    # Get analytics data
    total_users = User.objects.count()
    pending_listings = Land.objects.filter(status='pending').count()
    active_listings = Land.objects.filter(status='approved').count()
    total_inquiries = Inquiry.objects.count()
    
    # Recent activity (last 10 activities)
    recent_users = User.objects.order_by('-date_joined')[:5]
    recent_listings = Land.objects.order_by('-created_at')[:5]
    recent_inquiries = Inquiry.objects.order_by('-created_at')[:5]
    
    context = {
        'total_users': total_users,
        'pending_listings': pending_listings,
        'active_listings': active_listings,
        'total_inquiries': total_inquiries,
        'recent_users': recent_users,
        'recent_listings': recent_listings,
        'recent_inquiries': recent_inquiries,
    }
    
    return render(request, 'dashboards/admin_dashboard.html', context)


@login_required
def admin_user_management(request):
    """Admin user management view with search and filtering"""
    if not request.user.profile.role == 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('auth:dashboard_redirect')
    
    # Get search and filter parameters
    search_query = request.GET.get('search', '')
    role_filter = request.GET.get('role', '')
    status_filter = request.GET.get('status', '')
    
    # Build queryset
    users = User.objects.select_related('profile').all()
    
    if search_query:
        users = users.filter(
            Q(username__icontains=search_query) |
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(email__icontains=search_query)
        )
    
    if role_filter:
        users = users.filter(profile__role=role_filter)
    
    if status_filter == 'active':
        users = users.filter(is_active=True)
    elif status_filter == 'inactive':
        users = users.filter(is_active=False)
    
    # Pagination
    paginator = Paginator(users, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'role_filter': role_filter,
        'status_filter': status_filter,
        'role_choices': UserProfile.ROLE_CHOICES,
    }
    
    return render(request, 'dashboards/admin_user_management.html', context)


@login_required
@require_http_methods(["POST"])
def admin_toggle_user_status(request, user_id):
    """Toggle user active status via HTMX"""
    if not request.user.profile.role == 'admin':
        return JsonResponse({'error': 'Access denied'}, status=403)
    
    user = get_object_or_404(User, id=user_id)
    user.is_active = not user.is_active
    user.save()
    
    status_text = 'Active' if user.is_active else 'Inactive'
    status_class = 'bg-green-100 text-green-800' if user.is_active else 'bg-red-100 text-red-800'
    
    return render(request, 'components/user_status_badge.html', {
        'user': user,
        'status_text': status_text,
        'status_class': status_class
    })


@login_required
def admin_listing_approval(request):
    """Admin listing approval queue"""
    if not request.user.profile.role == 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('auth:dashboard_redirect')
    
    # Get filter parameters
    status_filter = request.GET.get('status', 'pending')
    search_query = request.GET.get('search', '')
    
    # Build queryset
    listings = Land.objects.select_related('owner', 'owner__profile').all()
    
    if status_filter:
        listings = listings.filter(status=status_filter)
    
    if search_query:
        listings = listings.filter(
            Q(title__icontains=search_query) |
            Q(location__icontains=search_query) |
            Q(owner__username__icontains=search_query)
        )
    
    # Pagination
    paginator = Paginator(listings, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
        'search_query': search_query,
        'status_choices': Land.STATUS_CHOICES,
    }
    
    return render(request, 'dashboards/admin_listing_approval.html', context)


@login_required
@require_http_methods(["POST"])
def admin_approve_listing(request, listing_id):
    """Approve listing via HTMX"""
    if not request.user.profile.role == 'admin':
        return JsonResponse({'error': 'Access denied'}, status=403)
    
    listing = get_object_or_404(Land, id=listing_id)
    listing.status = 'approved'
    listing.is_approved = True
    admin_notes_input = request.POST.get('admin_notes', '')
    if admin_notes_input:
        listing.admin_notes = admin_notes_input
    listing.save()
    
    return render(request, 'components/listing_status_badge.html', {
        'listing': listing
    })


@login_required
@require_http_methods(["POST"])
def admin_reject_listing(request, listing_id):
    """Reject listing via HTMX"""
    if not request.user.profile.role == 'admin':
        return JsonResponse({'error': 'Access denied'}, status=403)
    
    listing = get_object_or_404(Land, id=listing_id)
    listing.status = 'rejected'
    listing.is_approved = False
    admin_notes_input = request.POST.get('admin_notes', '')
    if admin_notes_input:
        listing.admin_notes = admin_notes_input
    listing.save()
    
    return render(request, 'components/listing_status_badge.html', {
        'listing': listing
    })


@login_required
def admin_analytics(request):
    """Admin analytics and reports dashboard"""
    if not request.user.profile.role == 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('auth:dashboard_redirect')
    
    # User statistics
    user_stats = {
        'total_users': User.objects.count(),
        'active_users': User.objects.filter(is_active=True).count(),
        'inactive_users': User.objects.filter(is_active=False).count(),
        'users_by_role': User.objects.values('profile__role').annotate(count=Count('id')),
    }
    
    # Listing statistics
    listing_stats = {
        'total_listings': Land.objects.count(),
        'pending_listings': Land.objects.filter(status='pending').count(),
        'approved_listings': Land.objects.filter(status='approved').count(),
        'rejected_listings': Land.objects.filter(status='rejected').count(),
        'sold_listings': Land.objects.filter(status='sold').count(),
        'listings_by_type': Land.objects.values('property_type').annotate(count=Count('id')),
    }
    
    # Inquiry statistics
    inquiry_stats = {
        'total_inquiries': Inquiry.objects.count(),
        'unread_inquiries': Inquiry.objects.filter(is_read=False).count(),
        'responded_inquiries': Inquiry.objects.exclude(seller_response='').count(),
    }
    
    context = {
        'user_stats': user_stats,
        'listing_stats': listing_stats,
        'inquiry_stats': inquiry_stats,
    }
    
    return render(request, 'dashboards/admin_analytics.html', context)


@login_required
def seller_dashboard(request):
    """Seller dashboard view with listing overview and inquiry tracking"""
    if not request.user.profile.role == 'seller':
        messages.error(request, 'Access denied. Seller privileges required.')
        return redirect('auth:dashboard_redirect')
    
    # Get seller's listings with statistics
    user_listings = Land.objects.filter(owner=request.user).select_related('owner')
    
    # Calculate statistics
    total_listings = user_listings.count()
    active_listings = user_listings.filter(status='approved').count()
    pending_listings = user_listings.filter(status='pending').count()
    draft_listings = user_listings.filter(status='draft').count()
    
    # Get recent listings (last 5)
    recent_listings = user_listings.order_by('-created_at')[:5]
    
    # Get inquiries for seller's listings
    seller_inquiries = Inquiry.objects.filter(
        land__owner=request.user
    ).select_related('buyer', 'land').order_by('-created_at')
    
    total_inquiries = seller_inquiries.count()
    unread_inquiries = seller_inquiries.filter(is_read=False).count()
    recent_inquiries = seller_inquiries[:5]
    
    context = {
        'total_listings': total_listings,
        'active_listings': active_listings,
        'pending_listings': pending_listings,
        'draft_listings': draft_listings,
        'recent_listings': recent_listings,
        'total_inquiries': total_inquiries,
        'unread_inquiries': unread_inquiries,
        'recent_inquiries': recent_inquiries,
    }
    
    return render(request, 'dashboards/seller_dashboard.html', context)


@login_required
def buyer_dashboard(request):
    """Buyer dashboard view with property search, favorites, and inquiry history"""
    if not request.user.profile.role == 'buyer':
        messages.error(request, 'Access denied. Buyer privileges required.')
        return redirect('auth:dashboard_redirect')
    
    # Get buyer's statistics
    favorites_count = Favorite.objects.filter(user=request.user).count()
    inquiries_count = Inquiry.objects.filter(buyer=request.user).count()
    unread_responses = Inquiry.objects.filter(
        buyer=request.user,
        seller_response__isnull=False
    ).exclude(seller_response='').count()
    
    # Get recent favorites
    recent_favorites = Favorite.objects.filter(
        user=request.user
    ).select_related('land', 'land__owner').order_by('-created_at')[:3]
    
    # Get recent inquiries
    recent_inquiries = Inquiry.objects.filter(
        buyer=request.user
    ).select_related('land', 'land__owner').order_by('-created_at')[:3]
    
    # Get recommended properties (approved listings, excluding user's own if they're also a seller)
    recommended_properties = Land.objects.filter(
        status='approved',
        is_approved=True
    ).exclude(
        owner=request.user
    ).select_related('owner').prefetch_related('images').order_by('-created_at')[:6]
    
    context = {
        'favorites_count': favorites_count,
        'inquiries_count': inquiries_count,
        'unread_responses': unread_responses,
        'recent_favorites': recent_favorites,
        'recent_inquiries': recent_inquiries,
        'recommended_properties': recommended_properties,
    }
    
    return render(request, 'dashboards/buyer_dashboard.html', context)


@login_required
def seller_listings(request):
    """Seller's listing management interface"""
    if not request.user.profile.role == 'seller':
        messages.error(request, 'Access denied. Seller privileges required.')
        return redirect('auth:dashboard_redirect')
    
    # Get filter parameters
    status_filter = request.GET.get('status', '')
    search_query = request.GET.get('search', '')
    
    # Build queryset
    listings = Land.objects.filter(owner=request.user).prefetch_related('images')
    
    if status_filter:
        listings = listings.filter(status=status_filter)
    
    if search_query:
        listings = listings.filter(
            Q(title__icontains=search_query) |
            Q(location__icontains=search_query) |
            Q(description__icontains=search_query)
        )
    
    # Pagination
    paginator = Paginator(listings, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
        'search_query': search_query,
        'status_choices': Land.STATUS_CHOICES,
    }
    
    return render(request, 'dashboards/seller_listings.html', context)


@login_required
def seller_create_listing(request):
    """Create new land listing"""
    if not request.user.profile.role == 'seller':
        messages.error(request, 'Access denied. Seller privileges required.')
        return redirect('auth:dashboard_redirect')
    
    if request.method == 'POST':
        form = LandListingForm(request.POST)
        image_form = MultipleImageUploadForm(request.POST, request.FILES)
        
        if form.is_valid() and image_form.is_valid():
            # Save the listing
            listing = form.save(commit=False)
            listing.owner = request.user
            listing.status = 'pending'  # Submit for approval
            listing.save()
            
            # Handle image uploads
            images = request.FILES.getlist('images')
            for i, image_file in enumerate(images):
                LandImage.objects.create(
                    land=listing,
                    image=image_file,
                    is_primary=(i == 0),  # First image is primary
                    order=i
                )
            
            messages.success(request, 'Your listing has been created and submitted for approval.')
            return redirect('seller_listings')
    else:
        form = LandListingForm()
        image_form = MultipleImageUploadForm()
    
    context = {
        'form': form,
        'image_form': image_form,
    }
    
    return render(request, 'dashboards/seller_create_listing.html', context)


@login_required
def seller_edit_listing(request, listing_id):
    """Edit existing land listing"""
    if not request.user.profile.role == 'seller':
        messages.error(request, 'Access denied. Seller privileges required.')
        return redirect('auth:dashboard_redirect')
    
    listing = get_object_or_404(Land, id=listing_id, owner=request.user)
    
    if request.method == 'POST':
        form = LandListingForm(request.POST, instance=listing)
        image_form = MultipleImageUploadForm(request.POST, request.FILES)
        
        if form.is_valid():
            # Check if significant changes were made that require re-approval
            original_listing = Land.objects.get(id=listing_id)
            significant_changes = (
                form.cleaned_data['price'] != original_listing.price or
                form.cleaned_data['size_acres'] != original_listing.size_acres or
                form.cleaned_data['property_type'] != original_listing.property_type or
                form.cleaned_data['location'] != original_listing.location
            )
            
            # Save the listing
            updated_listing = form.save(commit=False)
            
            # If significant changes, require re-approval
            if significant_changes and listing.status == 'approved':
                updated_listing.status = 'pending'
                updated_listing.is_approved = False
                messages.info(request, 'Your listing has been updated and submitted for re-approval due to significant changes.')
            else:
                messages.success(request, 'Your listing has been updated successfully.')
            
            updated_listing.save()
            
            # Handle new image uploads
            if image_form.is_valid():
                images = request.FILES.getlist('images')
                for i, image_file in enumerate(images):
                    # Get the current max order
                    max_order = listing.images.aggregate(max_order=Max('order'))['max_order'] or 0
                    LandImage.objects.create(
                        land=listing,
                        image=image_file,
                        order=max_order + i + 1
                    )
            
            return redirect('seller_listings')
    else:
        form = LandListingForm(instance=listing)
        image_form = MultipleImageUploadForm()
    
    context = {
        'form': form,
        'image_form': image_form,
        'listing': listing,
        'existing_images': listing.images.all().order_by('order'),
    }
    
    return render(request, 'dashboards/seller_edit_listing.html', context)


@login_required
@require_http_methods(["POST"])
def seller_delete_image(request, image_id):
    """Delete a listing image via HTMX"""
    if not request.user.profile.role == 'seller':
        return JsonResponse({'error': 'Access denied'}, status=403)
    
    image = get_object_or_404(LandImage, id=image_id, land__owner=request.user)
    image.delete()
    
    return JsonResponse({'success': True})


@login_required
@require_http_methods(["POST"])
def seller_set_primary_image(request, image_id):
    """Set primary image for a listing via HTMX"""
    if not request.user.profile.role == 'seller':
        return JsonResponse({'error': 'Access denied'}, status=403)
    
    image = get_object_or_404(LandImage, id=image_id, land__owner=request.user)
    
    # Remove primary status from all images of this listing
    LandImage.objects.filter(land=image.land).update(is_primary=False)
    
    # Set this image as primary
    image.is_primary = True
    image.save()
    
    return JsonResponse({'success': True})


@login_required
def seller_inquiries(request):
    """Seller's inquiry management system"""
    if not request.user.profile.role == 'seller':
        messages.error(request, 'Access denied. Seller privileges required.')
        return redirect('auth:dashboard_redirect')
    
    # Get filter parameters
    status_filter = request.GET.get('status', '')
    listing_filter = request.GET.get('listing', '')
    
    # Build queryset
    inquiries = Inquiry.objects.filter(
        land__owner=request.user
    ).select_related('buyer', 'land').order_by('-created_at')
    
    if status_filter == 'unread':
        inquiries = inquiries.filter(is_read=False)
    elif status_filter == 'responded':
        inquiries = inquiries.exclude(seller_response='')
    elif status_filter == 'pending':
        inquiries = inquiries.filter(seller_response='')
    
    if listing_filter:
        inquiries = inquiries.filter(land_id=listing_filter)
    
    # Pagination
    paginator = Paginator(inquiries, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get seller's listings for filter dropdown
    seller_listings = Land.objects.filter(owner=request.user).values('id', 'title')
    
    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
        'listing_filter': listing_filter,
        'seller_listings': seller_listings,
    }
    
    return render(request, 'dashboards/seller_inquiries.html', context)


@login_required
def seller_inquiry_detail(request, inquiry_id):
    """View and respond to a specific inquiry"""
    if not request.user.profile.role == 'seller':
        messages.error(request, 'Access denied. Seller privileges required.')
        return redirect('auth:dashboard_redirect')
    
    inquiry = get_object_or_404(
        Inquiry, 
        id=inquiry_id, 
        land__owner=request.user
    )
    
    # Mark as read
    if not inquiry.is_read:
        inquiry.is_read = True
        inquiry.save()
    
    if request.method == 'POST':
        form = InquiryResponseForm(request.POST, instance=inquiry)
        if form.is_valid():
            response = form.save(commit=False)
            response.response_date = timezone.now()
            response.save()
            
            messages.success(request, 'Your response has been sent to the buyer.')
            return redirect('seller_inquiries')
    else:
        form = InquiryResponseForm(instance=inquiry)
    
    context = {
        'inquiry': inquiry,
        'form': form,
    }
    
    return render(request, 'dashboards/seller_inquiry_detail.html', context)


@login_required
@require_http_methods(["POST"])
def seller_mark_inquiry_read(request, inquiry_id):
    """Mark inquiry as read via HTMX"""
    if not request.user.profile.role == 'seller':
        return JsonResponse({'error': 'Access denied'}, status=403)
    
    inquiry = get_object_or_404(
        Inquiry, 
        id=inquiry_id, 
        land__owner=request.user
    )
    
    inquiry.is_read = True
    inquiry.save()
    
    return render(request, 'components/inquiry_status_badge.html', {
        'inquiry': inquiry
    })


@login_required
def buyer_browse_listings(request):
    """Buyer property browsing with filtering"""
    if not request.user.profile.role == 'buyer':
        messages.error(request, 'Access denied. Buyer privileges required.')
        return redirect('auth:dashboard_redirect')
    
    # Get filter parameters
    search_query = request.GET.get('search', '')
    location_filter = request.GET.get('location', '')
    property_type_filter = request.GET.get('property_type', '')
    min_price = request.GET.get('min_price', '')
    max_price = request.GET.get('max_price', '')
    min_size = request.GET.get('min_size', '')
    max_size = request.GET.get('max_size', '')
    sort_by = request.GET.get('sort', 'newest')
    
    # Build queryset - only approved listings
    listings = Land.objects.filter(
        status='approved',
        is_approved=True
    ).exclude(
        owner=request.user  # Exclude user's own listings if they're also a seller
    ).select_related('owner').prefetch_related('images')
    
    # Apply filters
    if search_query:
        listings = listings.filter(
            Q(title__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(location__icontains=search_query)
        )
    
    if location_filter:
        listings = listings.filter(location__icontains=location_filter)
    
    if property_type_filter:
        listings = listings.filter(property_type=property_type_filter)
    
    if min_price:
        try:
            listings = listings.filter(price__gte=float(min_price))
        except ValueError:
            pass
    
    if max_price:
        try:
            listings = listings.filter(price__lte=float(max_price))
        except ValueError:
            pass
    
    if min_size:
        try:
            listings = listings.filter(size_acres__gte=float(min_size))
        except ValueError:
            pass
    
    if max_size:
        try:
            listings = listings.filter(size_acres__lte=float(max_size))
        except ValueError:
            pass
    
    # Apply sorting
    if sort_by == 'price_low':
        listings = listings.order_by('price')
    elif sort_by == 'price_high':
        listings = listings.order_by('-price')
    elif sort_by == 'size_low':
        listings = listings.order_by('size_acres')
    elif sort_by == 'size_high':
        listings = listings.order_by('-size_acres')
    elif sort_by == 'oldest':
        listings = listings.order_by('created_at')
    else:  # newest (default)
        listings = listings.order_by('-created_at')
    
    # Get user's favorites for heart icons
    user_favorites = set(
        Favorite.objects.filter(user=request.user).values_list('land_id', flat=True)
    )
    
    # Pagination
    paginator = Paginator(listings, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'location_filter': location_filter,
        'property_type_filter': property_type_filter,
        'min_price': min_price,
        'max_price': max_price,
        'min_size': min_size,
        'max_size': max_size,
        'sort_by': sort_by,
        'property_types': Land.PROPERTY_TYPES,
        'user_favorites': user_favorites,
        'total_count': listings.count(),
    }
    
    # For HTMX requests, return only the listings grid
    if request.headers.get('HX-Request'):
        return render(request, 'components/listings_grid.html', context)
    
    return render(request, 'dashboards/buyer_browse_listings.html', context)


@login_required
def buyer_listing_detail(request, listing_id):
    """Detailed property view with image gallery and contact options"""
    if not request.user.profile.role == 'buyer':
        messages.error(request, 'Access denied. Buyer privileges required.')
        return redirect('auth:dashboard_redirect')
    
    listing = get_object_or_404(
        Land,
        id=listing_id,
        status='approved',
        is_approved=True
    )
    
    # Check if user has favorited this listing
    is_favorited = Favorite.objects.filter(
        user=request.user,
        land=listing
    ).exists()
    
    # Get user's existing inquiry for this listing
    existing_inquiry = Inquiry.objects.filter(
        buyer=request.user,
        land=listing
    ).first()
    
    # Get related listings (same property type, different listing)
    related_listings = Land.objects.filter(
        property_type=listing.property_type,
        status='approved',
        is_approved=True
    ).exclude(
        id=listing.id
    ).exclude(
        owner=request.user
    ).select_related('owner').prefetch_related('images')[:4]
    
    context = {
        'listing': listing,
        'is_favorited': is_favorited,
        'existing_inquiry': existing_inquiry,
        'related_listings': related_listings,
    }
    
    return render(request, 'dashboards/buyer_listing_detail.html', context)


@login_required
def buyer_favorites(request):
    """Buyer's favorites management"""
    if not request.user.profile.role == 'buyer':
        messages.error(request, 'Access denied. Buyer privileges required.')
        return redirect('auth:dashboard_redirect')
    
    # Get filter parameters
    property_type_filter = request.GET.get('property_type', '')
    sort_by = request.GET.get('sort', 'newest')
    
    # Build queryset
    favorites = Favorite.objects.filter(
        user=request.user
    ).select_related('land', 'land__owner').prefetch_related('land__images')
    
    if property_type_filter:
        favorites = favorites.filter(land__property_type=property_type_filter)
    
    # Apply sorting
    if sort_by == 'price_low':
        favorites = favorites.order_by('land__price')
    elif sort_by == 'price_high':
        favorites = favorites.order_by('-land__price')
    elif sort_by == 'size_low':
        favorites = favorites.order_by('land__size_acres')
    elif sort_by == 'size_high':
        favorites = favorites.order_by('-land__size_acres')
    elif sort_by == 'oldest':
        favorites = favorites.order_by('created_at')
    else:  # newest (default)
        favorites = favorites.order_by('-created_at')
    
    # Pagination
    paginator = Paginator(favorites, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'property_type_filter': property_type_filter,
        'sort_by': sort_by,
        'property_types': Land.PROPERTY_TYPES,
    }
    
    return render(request, 'dashboards/buyer_favorites.html', context)


@login_required
@require_http_methods(["POST"])
def buyer_toggle_favorite(request, listing_id):
    """Toggle favorite status via HTMX"""
    if not request.user.profile.role == 'buyer':
        return JsonResponse({'error': 'Access denied'}, status=403)
    
    listing = get_object_or_404(Land, id=listing_id, status='approved', is_approved=True)
    
    favorite, created = Favorite.objects.get_or_create(
        user=request.user,
        land=listing
    )
    
    if not created:
        favorite.delete()
        is_favorited = False
    else:
        is_favorited = True
    
    return render(request, 'components/favorite_button.html', {
        'listing': listing,
        'is_favorited': is_favorited
    })


@login_required
def buyer_inquiries(request):
    """Buyer's inquiry history and management"""
    if not request.user.profile.role == 'buyer':
        messages.error(request, 'Access denied. Buyer privileges required.')
        return redirect('auth:dashboard_redirect')
    
    # Get filter parameters
    status_filter = request.GET.get('status', '')
    
    # Build queryset
    inquiries = Inquiry.objects.filter(
        buyer=request.user
    ).select_related('land', 'land__owner').order_by('-created_at')
    
    if status_filter == 'responded':
        inquiries = inquiries.exclude(seller_response='')
    elif status_filter == 'pending':
        inquiries = inquiries.filter(seller_response='')
    
    # Pagination
    paginator = Paginator(inquiries, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
    }
    
    return render(request, 'dashboards/buyer_inquiries.html', context)


@login_required
def buyer_send_inquiry(request, listing_id):
    """Send inquiry about a property"""
    if not request.user.profile.role == 'buyer':
        messages.error(request, 'Access denied. Buyer privileges required.')
        return redirect('auth:dashboard_redirect')
    
    listing = get_object_or_404(
        Land,
        id=listing_id,
        status='approved',
        is_approved=True
    )
    
    # Check if user already sent an inquiry for this listing
    existing_inquiry = Inquiry.objects.filter(
        buyer=request.user,
        land=listing
    ).first()
    
    if request.method == 'POST':
        from .forms import InquiryForm
        form = InquiryForm(request.POST)
        
        if form.is_valid():
            inquiry = form.save(commit=False)
            inquiry.buyer = request.user
            inquiry.land = listing
            inquiry.save()
            
            messages.success(request, 'Your inquiry has been sent to the seller.')
            return redirect('buyer_listing_detail', listing_id=listing.id)
    else:
        form = InquiryForm()
    
    context = {
        'form': form,
        'listing': listing,
        'existing_inquiry': existing_inquiry,
    }
    
    return render(request, 'dashboards/buyer_send_inquiry.html', context)