{% extends 'base.html' %}
{% load static form_tags %}

{% block title %}Create Account - LandHub{% endblock %}

{% block main_class %}bg-gray-50{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-5xl mx-auto">
        <div class="bg-white rounded-2xl shadow-2xl overflow-hidden md:grid md:grid-cols-2">
            <!-- Info Panel -->
            <div class="relative hidden md:block">
                <div class="absolute inset-0 bg-gradient-to-br from-primary-600 to-secondary-600 opacity-90"></div>
                <img class="absolute inset-0 w-full h-full object-cover" src="{% static 'images/register-bg.jpg' %}" alt="Land property background">
                <div class="relative p-12 text-white flex flex-col justify-between h-full">
                    <div>
                        <a href="{% url 'home' %}" class="inline-block mb-8">
                            <h1 class="text-3xl font-bold tracking-tight text-white">LandHub</h1>
                        </a>
                        <h2 class="text-4xl font-extrabold leading-tight">Start Your Land Journey With Us.</h2>
                        <p class="mt-4 text-lg opacity-80">Discover, buy, and sell land with confidence. Join a growing community of landowners and investors.</p>
                    </div>
                    <div class="mt-8">
                        <p class="text-sm font-medium opacity-70">&copy; {% now "Y" %} LandHub. All rights reserved.</p>
                    </div>
                </div>
            </div>

            <!-- Form Panel -->
            <div class="p-8 sm:p-12">
                <div class="w-full max-w-md mx-auto">
                    <div class="text-center md:text-left mb-8">
                        <h2 class="text-3xl font-extrabold text-gray-900">Create an Account</h2>
                        <p class="mt-2 text-sm text-gray-600">
                            Or <a href="{% url 'auth:login' %}" class="font-medium text-primary-600 hover:text-primary-500">sign in to your existing account</a>
                        </p>
                    </div>

                    <form class="space-y-6" method="POST">
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="bg-red-50 border-l-4 border-red-400 p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-red-700">
                                            {% for error in form.non_field_errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        {% endif %}

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                {{ form.first_name|add_label_class:"block text-sm font-medium text-gray-700" }}
                                {{ form.first_name|add_class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"|add_attr:"placeholder:John" }}
                                {% for error in form.first_name.errors %}<p class="mt-2 text-sm text-red-600">{{ error }}</p>{% endfor %}
                            </div>
                            <div>
                                {{ form.last_name|add_label_class:"block text-sm font-medium text-gray-700" }}
                                {{ form.last_name|add_class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"|add_attr:"placeholder:Doe" }}
                                {% for error in form.last_name.errors %}<p class="mt-2 text-sm text-red-600">{{ error }}</p>{% endfor %}
                            </div>
                        </div>

                        <div>
                            {{ form.username|add_label_class:"block text-sm font-medium text-gray-700" }}
                            {{ form.username|add_class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"|add_attr:"placeholder:johndoe" }}
                            {% for error in form.username.errors %}<p class="mt-2 text-sm text-red-600">{{ error }}</p>{% endfor %}
                        </div>

                        <div>
                            {{ form.email|add_label_class:"block text-sm font-medium text-gray-700" }}
                            {{ form.email|add_class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"|add_attr:"placeholder:<EMAIL>" }}
                            {% for error in form.email.errors %}<p class="mt-2 text-sm text-red-600">{{ error }}</p>{% endfor %}
                        </div>

                        <div>
                            {{ form.password1|add_label_class:"block text-sm font-medium text-gray-700" }}
                            {{ form.password1|add_class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"|add_attr:"placeholder:••••••••" }}
                            {% for error in form.password1.errors %}<p class="mt-2 text-sm text-red-600">{{ error }}</p>{% endfor %}
                        </div>

                        <div>
                            {{ form.password2|add_label_class:"block text-sm font-medium text-gray-700" }}
                            {{ form.password2|add_class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"|add_attr:"placeholder:••••••••" }}
                            {% for error in form.password2.errors %}<p class="mt-2 text-sm text-red-600">{{ error }}</p>{% endfor %}
                        </div>

                        <div class="flex items-center">
                            {{ form.agree_terms|add_class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" }}
                            <label for="{{ form.agree_terms.id_for_label }}" class="ml-2 block text-sm text-gray-900">
                                I agree to the <a href="#" class="font-medium text-primary-600 hover:text-primary-500">Terms</a> and <a href="#" class="font-medium text-primary-600 hover:text-primary-500">Privacy Policy</a>.
                            </label>
                        </div>
                        {% for error in form.agree_terms.errors %}<p class="mt-2 text-sm text-red-600">{{ error }}</p>{% endfor %}

                        <div>
                            <button type="submit" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors">
                                Create Account
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}